from django.shortcuts import render

# Create your views here.
from django.shortcuts import render, redirect
from .models import CartItem
from books.models import Book
from django.contrib import messages

def add_to_cart(request, book_id):
    book = Book.objects.get(id=book_id)
    user = request.user
    cart_item, created = CartItem.objects.get_or_create(book=book, user=user)
    if not created:
        cart_item.quantity += 1
        cart_item.save()
    messages.success(request, 'Book added to cart!')
    return redirect('book_detail', book_id=book_id)